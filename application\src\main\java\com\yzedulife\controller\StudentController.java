package com.yzedulife.controller;

import com.yzedulife.annotation.Token;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.convert.StudentClassAppConvert;
import com.yzedulife.convert.StudentSchoolAppConvert;
import com.yzedulife.convert.StudentUserAppConvert;
import com.yzedulife.response.Response;
import com.yzedulife.response.StudentClassResponse;
import com.yzedulife.response.StudentSchoolResponse;
import com.yzedulife.response.StudentUserResponse;
import com.yzedulife.service.dto.StudentClassDTO;
import com.yzedulife.service.dto.StudentSchoolDTO;
import com.yzedulife.service.dto.StudentUserDTO;
import com.yzedulife.service.service.StudentClassService;
import com.yzedulife.service.service.StudentSchoolService;
import com.yzedulife.service.service.StudentUserService;
import com.yzedulife.vo.StudentClassVO;
import com.yzedulife.vo.StudentSchoolVO;
import com.yzedulife.vo.StudentUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/student")
@Tag(name = "学生管理模块")
public class StudentController {

    @Autowired
    private StudentClassService studentClassService;

    @Autowired
    private StudentUserService studentUserService;

    @Autowired
    private StudentSchoolService studentSchoolService;

    // ==================== 班级管理接口 ====================

    @Token("admin")
    @Operation(summary = "创建班级")
    @PostMapping("/class/create")
    @Transactional
    public Response createClass(@Valid @RequestBody StudentClassVO studentClassVO) {
        try {
            StudentClassDTO studentClassDTO = StudentClassAppConvert.INSTANCE.vo2dto(studentClassVO);
            StudentClassDTO result = studentClassService.create(studentClassDTO);
            StudentClassResponse response = StudentClassAppConvert.INSTANCE.dto2response(result);
            return Response.success().data(response);
        } catch (BusinessException e) {
            log.error("创建班级失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("创建班级异常", e);
            return Response.error().msg("创建班级失败");
        }
    }

    @Token("admin")
    @Operation(summary = "删除班级")
    @PostMapping("/class/delete")
    @Transactional
    public Response deleteClass(@RequestParam Long id) {
        try {
            Boolean result = studentClassService.deleteById(id);
            if (result) {
                return Response.success().msg("删除班级成功");
            } else {
                return Response.error().msg("删除班级失败");
            }
        } catch (BusinessException e) {
            log.error("删除班级失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("删除班级异常", e);
            return Response.error().msg("删除班级失败");
        }
    }

    @Operation(summary = "获取所有班级")
    @GetMapping("/class/list")
    public Response getClassList() {
        try {
            List<StudentClassDTO> classList = studentClassService.getAll();
            List<StudentClassResponse> responseList = StudentClassAppConvert.INSTANCE.dto2responseList(classList);
            return Response.success().data(responseList);
        } catch (BusinessException e) {
            log.error("获取班级列表失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("获取班级列表异常", e);
            return Response.error().msg("获取班级列表失败");
        }
    }

    // ==================== 学生管理接口 ====================

    @Token("admin")
    @Operation(summary = "创建学生")
    @PostMapping("/user/create")
    @Transactional
    public Response createStudent(@Valid @RequestBody StudentUserVO studentUserVO) {
        try {
            StudentUserDTO studentUserDTO = StudentUserAppConvert.INSTANCE.vo2dto(studentUserVO);
            StudentUserDTO result = studentUserService.create(studentUserDTO);
            
            StudentUserResponse response = StudentUserAppConvert.INSTANCE.dto2response(result);
            
            return Response.success().data(response);
        } catch (BusinessException e) {
            log.error("创建学生失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("创建学生异常", e);
            return Response.error().msg("创建学生失败");
        }
    }

    @Token("admin")
    @Operation(summary = "修改学生")
    @PostMapping("/user/update")
    @Transactional
    public Response updateStudent(@Valid @RequestBody StudentUserVO studentUserVO) {
        try {
            if (studentUserVO.getId() == null) {
                return Response.error().msg("学生ID不能为空");
            }
            
            StudentUserDTO studentUserDTO = StudentUserAppConvert.INSTANCE.vo2dto(studentUserVO);
            StudentUserDTO result = studentUserService.update(studentUserDTO);
            
            StudentUserResponse response = StudentUserAppConvert.INSTANCE.dto2response(result);
            
            return Response.success().data(response);
        } catch (BusinessException e) {
            log.error("修改学生失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("修改学生异常", e);
            return Response.error().msg("修改学生失败");
        }
    }

    @Token("admin")
    @Operation(summary = "删除学生")
    @PostMapping("/user/delete")
    @Transactional
    public Response deleteStudent(@RequestParam Long id) {
        try {
            Boolean result = studentUserService.deleteById(id);
            if (result) {
                return Response.success().msg("删除学生成功");
            } else {
                return Response.error().msg("删除学生失败");
            }
        } catch (BusinessException e) {
            log.error("删除学生失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("删除学生异常", e);
            return Response.error().msg("删除学生失败");
        }
    }

    @Token("admin")
    @Operation(summary = "查看学生")
    @GetMapping("/user/list")
    public Response getStudentList() {
        try {
            // 获取所有班级
            List<StudentClassDTO> classList = studentClassService.getAll();
            List<StudentClassResponse> responseList = new ArrayList<>();

            // 为每个班级填充学生信息
            for (StudentClassDTO studentClass : classList) {
                StudentClassResponse response = StudentClassAppConvert.INSTANCE.dto2response(studentClass);

                // 获取该班级的所有学生
                List<StudentUserDTO> students = studentUserService.getByClassId(studentClass.getId());
                List<StudentUserResponse> studentResponses = StudentUserAppConvert.INSTANCE.dto2responseList(students);

                response.setStudents(studentResponses);
                responseList.add(response);
            }

            return Response.success().data(responseList);
        } catch (BusinessException e) {
            log.error("获取班级学生失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("获取班级学生异常", e);
            return Response.error().msg("获取班级学生失败");
        }
    }

    // ==================== 学校管理接口 ====================

    @Token("admin")
    @Operation(summary = "创建学校")
    @PostMapping("/school/create")
    @Transactional
    public Response createSchool(@Valid @RequestBody StudentSchoolVO studentSchoolVO) {
        try {
            StudentSchoolDTO studentSchoolDTO = StudentSchoolAppConvert.INSTANCE.vo2dto(studentSchoolVO);
            StudentSchoolDTO result = studentSchoolService.create(studentSchoolDTO);
            StudentSchoolResponse response = StudentSchoolAppConvert.INSTANCE.dto2response(result);
            return Response.success().data(response);
        } catch (BusinessException e) {
            log.error("创建学校失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("创建学校异常", e);
            return Response.error().msg("创建学校失败");
        }
    }

    @Token("admin")
    @Operation(summary = "更新学校")
    @PostMapping("/school/update")
    @Transactional
    public Response updateSchool(@Valid @RequestBody StudentSchoolVO studentSchoolVO) {
        try {
            if (studentSchoolVO.getId() == null) {
                return Response.error().msg("学校ID不能为空");
            }

            StudentSchoolDTO studentSchoolDTO = StudentSchoolAppConvert.INSTANCE.vo2dto(studentSchoolVO);
            StudentSchoolDTO result = studentSchoolService.update(studentSchoolDTO);
            StudentSchoolResponse response = StudentSchoolAppConvert.INSTANCE.dto2response(result);
            return Response.success().data(response);
        } catch (BusinessException e) {
            log.error("更新学校失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("更新学校异常", e);
            return Response.error().msg("更新学校失败");
        }
    }

    @Token("admin")
    @Operation(summary = "删除学校")
    @PostMapping("/school/delete")
    @Transactional
    public Response deleteSchool(@RequestParam Long id) {
        try {
            Boolean result = studentSchoolService.deleteById(id);
            if (result) {
                return Response.success().msg("删除学校成功");
            } else {
                return Response.error().msg("删除学校失败");
            }
        } catch (BusinessException e) {
            log.error("删除学校失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("删除学校异常", e);
            return Response.error().msg("删除学校失败");
        }
    }

    @Operation(summary = "获取所有学校")
    @GetMapping("/school/list")
    public Response getSchoolList() {
        try {
            List<StudentSchoolDTO> schoolList = studentSchoolService.getAll();
            List<StudentSchoolResponse> responseList = StudentSchoolAppConvert.INSTANCE.dto2responseList(schoolList);
            return Response.success().data(responseList);
        } catch (BusinessException e) {
            log.error("获取学校列表失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("获取学校列表异常", e);
            return Response.error().msg("获取学校列表失败");
        }
    }
}
